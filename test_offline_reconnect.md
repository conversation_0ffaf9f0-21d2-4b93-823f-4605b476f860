# Test Plan: Offline Mode Reconnection

## Overview
This test plan verifies that when a user was in offline mode and reopens the app, the app attempts to reconnect to online mode first before offering the offline choice again.

## Test Scenarios

### Scenario 1: User was in offline mode, app reopens with good connection
1. **Setup**: User was previously in offline mode (`is_offline_mode` = 'true' in AsyncStorage)
2. **Action**: User closes and reopens the app
3. **Expected Result**: 
   - App detects previous offline mode
   - Clears offline mode flag
   - Attempts auto-login with stored credentials
   - Successfully logs in and navigates to Portal

### Scenario 2: User was in offline mode, app reopens with no connection
1. **Setup**: User was previously in offline mode (`is_offline_mode` = 'true' in AsyncStorage)
2. **Action**: User closes and reopens the app (no internet connection)
3. **Expected Result**:
   - App detects previous offline mode
   - Clears offline mode flag
   - Attempts network connectivity check
   - Fails connectivity check
   - Shows OfflineChoiceScreen with reason 'offline_retry'
   - Shows special message: "We tried to reconnect you to the online services but encountered an issue..."

### Scenario 3: User was in offline mode, app reopens with connection but server timeout
1. **Setup**: User was previously in offline mode (`is_offline_mode` = 'true' in AsyncStorage)
2. **Action**: User closes and reopens the app (internet available but server slow)
3. **Expected Result**:
   - App detects previous offline mode
   - Clears offline mode flag
   - Attempts auto-login
   - Times out after 10 seconds
   - Shows OfflineChoiceScreen with reason 'offline_retry'
   - Shows special message about reconnection attempt

### Scenario 4: User was NOT in offline mode, normal app startup
1. **Setup**: User was not in offline mode (`is_offline_mode` not set or 'false')
2. **Action**: User closes and reopens the app
3. **Expected Result**:
   - App proceeds with normal auto-login flow
   - If connection fails, shows normal offline choice reasons (not 'offline_retry')

## Implementation Details

### Key Changes Made:

1. **LoginScreen.js**:
   - Added `wasInOfflineMode` state to track if user was previously offline
   - Added offline mode detection in `loadStoredCredentials()`
   - Clears `is_offline_mode` flag when detected
   - Added `getOfflineChoiceReason()` helper function
   - Updated all OfflineChoice navigation calls to use appropriate reason

2. **OfflineChoiceScreen.js**:
   - Added 'offline_retry' reason case
   - Added `getDescriptionText()` function for context-aware messaging
   - Updated "Try Login" button to only clear offline mode flag (not all data)

### Testing the Implementation:

1. **Manual Testing**:
   - Put app in offline mode
   - Close app completely
   - Reopen app with different network conditions
   - Verify behavior matches expected scenarios

2. **AsyncStorage Verification**:
   ```javascript
   // Check if offline mode flag is properly cleared
   const offlineFlag = await AsyncStorage.getItem('is_offline_mode');
   console.log('Offline flag after reconnect attempt:', offlineFlag);
   ```

3. **Log Verification**:
   - Look for "📴 User was previously in offline mode" message
   - Look for "🌐 Cleared offline mode flag" message
   - Verify reason passed to OfflineChoice is 'offline_retry' when appropriate

## Expected Log Output

When user was in offline mode and reopens app:
```
📴 User was previously in offline mode - attempting to go online first
🌐 Cleared offline mode flag - attempting online login
🔍 Auto-login credentials loaded: { ..., wasOfflineMode: 'YES' }
```

If connection fails:
```
⏰ Auto-login timeout reached
[Navigation to OfflineChoice with reason: 'offline_retry']
```

## Success Criteria

✅ App detects previous offline mode state
✅ Offline mode flag is cleared on reconnection attempt  
✅ Appropriate reason is passed to OfflineChoiceScreen
✅ Special messaging is shown for reconnection attempts
✅ "Try Login" preserves credentials and only clears offline flag
✅ Normal offline flow is preserved for non-offline-retry cases
