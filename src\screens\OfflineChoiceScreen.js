import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Dimensions,
  Alert,
  Image,
  StatusBar
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useOffline } from '../contexts/OfflineContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Svg, { Path, Circle } from 'react-native-svg';

const { width, height } = Dimensions.get('window');

// WiFi Off Icon Component
const WiFiOffIcon = ({ size = 48, color = '#EAB308' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.08 2.93 1 9z" stroke={color} strokeWidth="2" fill="none"/>
    <Path d="M5 13l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z" stroke={color} strokeWidth="2" fill="none"/>
    <Path d="M9 17l2 2c.87-.87 2.13-.87 3 0l2-2C14.24 15.24 9.76 15.24 9 17z" stroke={color} strokeWidth="2" fill="none"/>
    <Path d="M2 2l20 20" stroke={color} strokeWidth="2"/>
  </Svg>
);

// Offline Icon Component
const OfflineIcon = ({ size = 24, color = '#EAB308' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Circle cx="12" cy="12" r="10" stroke={color} strokeWidth="2" fill="none"/>
    <Path d="M8 12h8" stroke={color} strokeWidth="2"/>
  </Svg>
);

// Login Icon Component
const LoginIcon = ({ size = 24, color = '#EAB308' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4" stroke={color} strokeWidth="2" fill="none"/>
    <Path d="M10 17l5-5-5-5" stroke={color} strokeWidth="2" fill="none"/>
    <Path d="M15 12H3" stroke={color} strokeWidth="2" fill="none"/>
  </Svg>
);

const OfflineChoiceScreen = ({ navigation, route }) => {
  const { theme, currentThemeName } = useTheme();
  const { enterOfflineMode } = useOffline();

  // Add fallback for currentThemeName
  const safeCurrentThemeName = currentThemeName || 'dark';
  
  // Get the reason for showing this screen from route params
  const { reason = 'connection_failed' } = route.params || {};

  const handleOfflineMode = async () => {
    try {
      console.log('📴 User chose offline mode');
      
      // Enter offline mode
      await enterOfflineMode();
      
      // Navigate to Portal with offline mode enabled
      navigation.reset({
        index: 0,
        routes: [{ name: 'Portal', params: { isOfflineMode: true } }],
      });
    } catch (error) {
      console.log('❌ Error entering offline mode:', error);
      Alert.alert('Error', 'Failed to enter offline mode. Please try again.');
    }
  };

  const handleLogin = async () => {
    try {
      console.log('🔑 User chose to try login again');

      // Only clear offline mode flag, preserve credentials and other data
      await AsyncStorage.removeItem('is_offline_mode');
      console.log('🌐 Cleared offline mode flag - will attempt online login');

      // Navigate to login screen
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    } catch (error) {
      console.log('❌ Error clearing offline mode:', error);
      Alert.alert('Error', 'Failed to clear offline mode. Please try again.');
    }
  };

  const getReasonText = () => {
    switch (reason) {
      case 'no_connection':
        return 'No internet connection detected';
      case 'server_timeout':
        return 'Server is taking too long to respond';
      case 'server_error':
        return 'Server error occurred';
      case 'connection_lost':
        return 'Internet connection was lost';
      case 'offline_retry':
        return 'Attempting to reconnect after offline mode';
      default:
        return 'Unable to connect to GUC servers';
    }
  };

  const getDescriptionText = () => {
    if (reason === 'offline_retry') {
      return 'We tried to reconnect you to the online services but encountered an issue. You can continue using the app in offline mode with your saved data, or try to login again.\n\nIn offline mode, some features like CMS and Mail will not be available.';
    }
    return 'You can continue using the app in offline mode with your saved data, or try to login again.\n\nIn offline mode, some features like CMS and Mail will not be available.';
  };

  const styles = createStyles(theme, safeCurrentThemeName);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={safeCurrentThemeName === 'light' ? 'dark-content' : 'light-content'}
        backgroundColor={theme.colors.background}
      />
      <View style={styles.content}>
        {/* Logo Section */}
        <View style={styles.logoSection}>
          <Image
            source={require('../../assets/myGUCTransparent.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        {/* Connection Issue Icon */}
        <View style={styles.iconSection}>
          <WiFiOffIcon size={64} color={theme.colors.primary} />
        </View>

        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Connection Issue</Text>
          <Text style={styles.subtitle}>{getReasonText()}</Text>
        </View>

        {/* Description */}
        <View style={styles.description}>
          <Text style={styles.descriptionText}>
            {getDescriptionText()}
          </Text>
        </View>

        {/* Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.offlineButton]}
            onPress={handleOfflineMode}
            activeOpacity={0.8}
          >
            <View style={styles.buttonIconContainer}>
              <OfflineIcon size={24} color={styles.offlineButtonText.color} />
            </View>
            <Text style={styles.offlineButtonText}>Offline Mode</Text>
            <Text style={[styles.buttonSubtext, styles.offlineButtonSubtext]}>Use saved data only</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.loginButton]}
            onPress={handleLogin}
            activeOpacity={0.8}
          >
            <View style={styles.buttonIconContainer}>
              <LoginIcon size={24} color={styles.loginButtonText.color} />
            </View>
            <Text style={styles.loginButtonText}>Login</Text>
            <Text style={[styles.buttonSubtext, styles.loginButtonSubtext]}>Try to login again</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme, currentThemeName) => {
  // Get screen dimensions for responsive design
  const { width, height } = Dimensions.get('window');
  const isSmallScreen = height < 700;
  const isVerySmallScreen = height < 600;
  const isNarrowScreen = width < 350;

  // Calculate responsive scaling factors
  const fontScale = isVerySmallScreen ? 0.85 : isSmallScreen ? 0.9 : 1.0;
  const spacingScale = isVerySmallScreen ? 0.7 : isSmallScreen ? 0.8 : 1.0;
  const paddingScale = isVerySmallScreen ? 0.6 : isSmallScreen ? 0.75 : 1.0;

  // Theme-specific colors
  const getThemeColors = () => {
    switch (currentThemeName) {
      case 'light':
        return {
          primary: '#EAB308',
          primaryText: '#FFFFFF',
          background: '#FFFFFF',
          surface: '#F8F9FA',
          text: '#1F2937',
          textSecondary: '#6B7280',
          border: '#E5E7EB',
          shadow: '#000000'
        };
      case 'pink':
        return {
          primary: '#EAB308',
          primaryText: '#1F2937',
          background: '#FCE7F3',
          surface: '#F9A8D4',
          text: '#EAB308',
          textSecondary: '#14B8A6',
          border: '#EAB308',
          shadow: '#000000'
        };
      case 'navy':
        return {
          primary: '#EF4444',
          primaryText: '#FFFFFF',
          background: '#1E293B',
          surface: '#334155',
          text: '#F1F5F9',
          textSecondary: '#CBD5E1',
          border: '#475569',
          shadow: '#000000'
        };
      default: // dark
        return {
          primary: '#EAB308',
          primaryText: '#1F2937',
          background: '#2C2C2C',
          surface: '#3A3A3A',
          text: '#FFFFFF',
          textSecondary: '#B0B0B0',
          border: '#555555',
          shadow: '#1A1A1A'
        };
    }
  };

  const colors = getThemeColors();

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    content: {
      flex: 1,
      paddingHorizontal: Math.max(15, 30 * paddingScale),
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: Math.max(10, 20 * paddingScale),
    },
    logoSection: {
      alignItems: 'center',
      marginBottom: Math.max(15, 30 * spacingScale),
    },
    logo: {
      width: Math.max(60, 80 * fontScale),
      height: Math.max(60, 80 * fontScale),
      marginBottom: Math.max(5, 10 * spacingScale),
    },
    appTitle: {
      fontSize: Math.max(18, 24 * fontScale),
      fontWeight: 'bold',
      color: colors.primary,
      letterSpacing: 1,
    },
    iconSection: {
      alignItems: 'center',
      marginBottom: Math.max(15, 30 * spacingScale),
      padding: Math.max(12, 20 * paddingScale),
      backgroundColor: colors.surface,
      borderRadius: 50,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    header: {
      alignItems: 'center',
      marginBottom: Math.max(15, 30 * spacingScale),
    },
    title: {
      fontSize: Math.max(20, 28 * fontScale),
      fontWeight: 'bold',
      color: colors.text,
      marginBottom: Math.max(5, 10 * spacingScale),
      textAlign: 'center',
    },
    subtitle: {
      fontSize: Math.max(14, 16 * fontScale),
      color: colors.textSecondary,
      textAlign: 'center',
      fontWeight: '500',
    },
    description: {
      marginBottom: Math.max(20, 40 * spacingScale),
      paddingHorizontal: Math.max(12, 20 * paddingScale),
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: Math.max(12, 20 * paddingScale),
      borderWidth: 1,
      borderColor: colors.border,
    },
    descriptionText: {
      fontSize: Math.max(14, 16 * fontScale),
      color: colors.text,
      textAlign: 'center',
      lineHeight: Math.max(18, 24 * fontScale),
    },
    buttonContainer: {
      width: '100%',
      gap: Math.max(10, 16 * spacingScale),
    },
    button: {
      paddingVertical: Math.max(12, 20 * paddingScale),
      paddingHorizontal: Math.max(18, 30 * paddingScale),
      borderRadius: 16,
      alignItems: 'center',
      minHeight: Math.max(70, 90 * spacingScale),
      justifyContent: 'center',
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 5,
    },
    buttonIconContainer: {
      marginBottom: Math.max(4, 8 * spacingScale),
    },
    offlineButton: {
      backgroundColor: colors.primary,
      borderWidth: 2,
      borderColor: colors.primary,
    },
    loginButton: {
      backgroundColor: colors.surface,
      borderWidth: 2,
      borderColor: colors.primary,
    },
    offlineButtonText: {
      fontSize: Math.max(16, 18 * fontScale),
      fontWeight: 'bold',
      color: colors.primaryText,
      marginBottom: Math.max(2, 4 * spacingScale),
    },
    loginButtonText: {
      fontSize: Math.max(16, 18 * fontScale),
      fontWeight: 'bold',
      color: colors.primary,
      marginBottom: Math.max(2, 4 * spacingScale),
    },
    buttonSubtext: {
      fontSize: Math.max(12, 14 * fontScale),
      opacity: 0.8,
    },
    offlineButtonSubtext: {
      color: colors.primaryText,
    },
    loginButtonSubtext: {
      color: colors.textSecondary,
    },
    footer: {
      marginTop: Math.max(15, 30 * spacingScale),
      paddingHorizontal: Math.max(12, 20 * paddingScale),
      paddingVertical: Math.max(10, 15 * paddingScale),
      backgroundColor: colors.surface,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.border,
    },
    footerText: {
      fontSize: Math.max(11, 13 * fontScale),
      color: colors.textSecondary,
      textAlign: 'center',
      fontStyle: 'italic',
      lineHeight: Math.max(14, 18 * fontScale),
    },
  });
};

export default OfflineChoiceScreen;
