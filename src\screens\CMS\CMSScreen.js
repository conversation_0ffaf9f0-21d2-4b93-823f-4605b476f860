import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Alert,
  Platform,
  ScrollView,
  PanResponder,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import RefreshIcon from '../../components/RefreshIcon';
import BackIcon from '../../components/BackIcon';
import BooksIcon from '../../components/BooksIcon';


import { clearWebViewSession, disposeWebView } from '../../utils/WebViewUtils';
import { useTheme } from '../../contexts/ThemeContext';
import { useOffline } from '../../contexts/OfflineContext';
import { isDemoUser } from '../../utils/DemoData';
import ThemeTransitionWrapper from '../../components/ThemeTransitionWrapper';
import { checkDemoMode, DEMO_CMS_DATA } from '../../utils/DemoData';
import { getResponsiveHeaderLayout, getContentPaddingForHeader } from '../../utils/HeaderUtils';

// Shimmer Component for ghost loading effect
const ShimmerBox = ({ style, theme }) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const shimmer = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(shimmerAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(shimmerAnim, {
            toValue: 0,
            duration: 1500,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };
    shimmer();
  }, [shimmerAnim]);

  const shimmerTranslate = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-100, 100],
  });

  const shimmerOpacity = shimmerAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.3, 0.7, 0.3],
  });

  return (
    <View style={[style, { overflow: 'hidden' }]}>
      <Animated.View
        style={[
          {
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: theme.colors.textSecondary,
            opacity: shimmerOpacity,
            transform: [{ translateX: shimmerTranslate }],
          },
        ]}
      />
      <Animated.View
        style={[
          {
            position: 'absolute',
            top: 0,
            left: -50,
            width: 50,
            height: '100%',
            backgroundColor: 'rgba(255, 255, 255, 0.4)',
            transform: [{ translateX: shimmerTranslate }, { skewX: '-20deg' }],
          },
        ]}
      />
    </View>
  );
};

// Semester Component to display each semester with its courses
const SemesterComponent = ({ semester, theme, safeCurrentThemeName, navigation, webViewRef, setIsNavigatingToCourse }) => {
  const [selectedCourse, setSelectedCourse] = useState(null);

  const handleCoursePress = async (course, index) => {
    console.log('Course pressed:', course.name, course.code);

    // Set visual selection
    setSelectedCourse(selectedCourse === index ? null : index);

    // Check if we're in demo mode
    const isDemoMode = await checkDemoMode();
    if (isDemoMode) {
      console.log('🎭 Demo mode - navigating to demo course content');

      // Navigate to ContentScreen with demo data
      setTimeout(() => {
        navigation.navigate('ContentScreen', {
          courseCode: course.code,
          courseName: course.name,
          isDemoMode: true
        });
      }, 200); // Small delay for visual feedback
      return;
    }

    // Set flag to prevent further data extraction
    setIsNavigatingToCourse(true);

    // Click on the "View Course" button in the WebView for this course
    if (webViewRef.current) {
      const jsCode = `
        (function() {
          try {
            // Find all course rows and look for the one with matching course code and id
            const courseRows = document.querySelectorAll('tbody tr');
            let targetRow = null;

            for (let row of courseRows) {
              const cells = row.querySelectorAll('td');
              // Check if this row contains our course code
              let hasMatchingCode = false;
              let hasMatchingId = false;

              for (let cell of cells) {
                if (cell.textContent.includes('${course.code}')) {
                  hasMatchingCode = true;
                }
                // Check hidden cells for matching id
                if (cell.style.display === 'none' && cell.textContent.trim() === '${course.id}') {
                  hasMatchingId = true;
                }
              }

              if (hasMatchingCode && hasMatchingId) {
                targetRow = row;
                break;
              }
            }

            if (targetRow) {
              // Look for "View Course" button in this row
              const viewCourseButton = targetRow.querySelector('input[value="View Course"]');
              if (viewCourseButton) {
                console.log('Clicking View Course button for: ${course.code} (ID: ${course.id})');
                viewCourseButton.click();
                return { success: true, message: 'Clicked View Course for ${course.code}' };
              } else {
                return { success: false, message: 'View Course button not found for ${course.code}' };
              }
            } else {
              return { success: false, message: 'Course row not found for ${course.code}' };
            }
          } catch (error) {
            return { success: false, message: 'Error clicking course: ' + error.message };
          }
        })();
      `;

      webViewRef.current.injectJavaScript(jsCode);

      // Navigate immediately to ContentScreen
      navigation.navigate('ContentScreen', {
        course: course,
      });
    } else {
      console.log('WebView ref not available');
      // Navigate anyway for testing
      navigation.navigate('ContentScreen', {
        course: course,
      });
    }
  };

  return (
    <View style={[
      semesterStyles.semesterContainer,
      {
        backgroundColor: theme.colors.surface,
        borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
        borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 1,
      }
    ]}>
      <Text style={[
        semesterStyles.semesterTitle,
        { color: theme.colors.text }
      ]}>
        {semester.season} - {semester.title}
      </Text>

      <View style={semesterStyles.coursesList}>
        {semester.courses.map((course, index) => (
          <TouchableOpacity
            key={index}
            style={[
              semesterStyles.courseItem,
              {
                backgroundColor: selectedCourse === index ? theme.colors.primary : theme.colors.surface,
                borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
                borderWidth: safeCurrentThemeName === 'colorful' ? 2 : 1,
              }
            ]}
            onPress={() => handleCoursePress(course, index)}
          >
            <View style={semesterStyles.courseContent}>
              <View style={semesterStyles.courseIconContainer}>
                <BooksIcon size={32} />
              </View>
              <View style={semesterStyles.courseTextContainer}>
                <Text
                  style={[
                    semesterStyles.courseName,
                    {
                      color: selectedCourse === index ? (safeCurrentThemeName === 'colorful' ? theme.colors.text : '#000000') : theme.colors.text
                    }
                  ]}
                  numberOfLines={2}
                  ellipsizeMode="tail"
                >
                  {course.name}
                </Text>
              </View>
              <View style={semesterStyles.courseCodeContainer}>
                <Text style={[
                  semesterStyles.courseCode,
                  {
                    color: selectedCourse === index ? (safeCurrentThemeName === 'colorful' ? theme.colors.primary : '#000000') : theme.colors.primary
                  }
                ]}>
                  {course.code}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

// Styles for SemesterComponent
const semesterStyles = StyleSheet.create({
  semesterContainer: {
    marginBottom: 20,
    borderRadius: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  semesterTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  coursesList: {
    // Remove maxHeight to show all courses
  },
  courseItem: {
    marginBottom: 10,
    borderRadius: 10,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  courseContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  courseIconContainer: {
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
    width: 24, // Fixed width for icon
  },
  courseTextContainer: {
    flex: 1,
    marginRight: 15,
    minWidth: 0, // Allow text to shrink
  },
  courseCodeContainer: {
    justifyContent: 'center',
    alignItems: 'flex-end',
    minWidth: 80, // Minimum width for course code
  },
  courseName: {
    fontSize: 16,
    fontWeight: '500',
  },
  courseCode: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'right',
    minWidth: 70, // Ensure code has enough space
  },
});

const CMSScreen = ({ navigation }) => {
  // Theme context
  const { theme, currentThemeName } = useTheme();
  const safeCurrentThemeName = currentThemeName || 'dark';
  const { checkNetworkConnectivity, isOfflineMode } = useOffline();



  // State management
  const [refreshRotation] = useState(new Animated.Value(0));
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isBackgroundRefreshing, setIsBackgroundRefreshing] = useState(false);
  const [cmsUrl, setCmsUrl] = useState('');
  const [semestersData, setSemestersData] = useState([]);
  const [hasCachedData, setHasCachedData] = useState(false);
  const [isNavigatingToCourse, setIsNavigatingToCourse] = useState(false);
  const [showWebView] = useState(false); // Hide WebView

  // Refs
  const webViewRef = useRef(null);
  const isMountedRef = useRef(true); // Track if component is still mounted

  // Cleanup function to kill all background operations
  const killAllBackgroundOperations = async () => {
    console.log('🛑 CMS: Killing all background operations...');

    // Mark component as unmounted
    isMountedRef.current = false;

    // Stop all animations
    refreshRotation.stopAnimation();

    // Reset all loading states to prevent cache clearing
    setIsRefreshing(false);
    setIsBackgroundRefreshing(false);

    // Dispose of WebView references
    await disposeWebView(webViewRef, 'cms-webview');

    console.log('✅ CMS: All background operations killed');
  };

  // Safe state setter that checks if component is still mounted
  const safeSetState = (setter, value, stateName) => {
    if (isMountedRef.current) {
      setter(value);
    } else {
      console.log(`⚠️ CMS: Prevented ${stateName} state update after unmount`);
    }
  };

  // Load credentials and setup CMS on component mount
  useEffect(() => {
    loadCredentialsAndSetupCMS();
  }, []);

  // Reset navigation flag when screen comes into focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      setIsNavigatingToCourse(false);
    });

    return unsubscribe;
  }, [navigation]);

  // Kill background operations when navigating away
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      console.log('🧹 CMS: Screen unmounting - Killing all background operations...');
      await killAllBackgroundOperations();
    });

    return unsubscribe;
  }, [navigation]);

  // Handle screen focus/blur - kill background operations when losing focus
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 CMS: Screen focused');

      // Mark component as mounted when focused
      isMountedRef.current = true;

      // Return cleanup function that runs when screen loses focus
      return () => {
        console.log('🔄 CMS: Screen losing focus - killing background operations...');
        killAllBackgroundOperations();
      };
    }, [])
  );

  // Start/stop rotation animation based on loading states
  useEffect(() => {
    if (isLoading || isRefreshing || isBackgroundRefreshing) {
      startRotationAnimation();
    } else {
      stopRotationAnimation();
    }
  }, [isLoading, isRefreshing, isBackgroundRefreshing]);

  // Network connectivity monitoring - check every 10 seconds
  useEffect(() => {
    const checkConnectionPeriodically = async () => {
      // Skip connectivity check if component is unmounted
      if (!isMountedRef.current) return;

      // Skip connectivity check if we're already in offline mode
      if (isOfflineMode) {
        return;
      }

      try {
        // Check if we're in demo mode - demo users don't need connectivity checks
        const storedUsername = await AsyncStorage.getItem('guc_username');
        const storedPassword = await AsyncStorage.getItem('guc_password');

        if (storedUsername && storedPassword) {
          const isDemo = await isDemoUser(storedUsername, storedPassword);
          if (isDemo) {
            return; // Skip connectivity check for demo users
          }
        }

        const hasConnection = await checkNetworkConnectivity();
        if (!hasConnection && isMountedRef.current) {
          console.log('❌ CMS: Connection lost - redirecting to offline choice');
          navigation.navigate('OfflineChoice', { reason: 'connection_lost' });
        }
      } catch (error) {
        console.log('❌ CMS: Error checking connectivity:', error);
      }
    };

    // Set up interval - check every 10 seconds
    const connectionCheckInterval = setInterval(checkConnectionPeriodically, 10000);

    return () => {
      if (connectionCheckInterval) {
        clearInterval(connectionCheckInterval);
        console.log('🔴 CMS: Cleared connectivity check interval');
      }
    };
  }, [navigation, checkNetworkConnectivity, isOfflineMode]);

  // Load cached data function
  const loadCachedData = async () => {
    try {
      const cachedSemesters = await AsyncStorage.getItem('cms_semesters_cache');
      if (cachedSemesters) {
        const parsedData = JSON.parse(cachedSemesters);
        console.log('Loaded cached CMS data:', parsedData.length, 'semesters');
        setSemestersData(parsedData);
        setHasCachedData(true);
        return true;
      }
      return false;
    } catch (error) {
      console.log('Error loading cached CMS data:', error);
      return false;
    }
  };

  // Save data to cache function
  const saveCacheData = async (data) => {
    try {
      await AsyncStorage.setItem('cms_semesters_cache', JSON.stringify(data));
      console.log('CMS data cached successfully');
    } catch (error) {
      console.log('Error caching CMS data:', error);
    }
  };

  // Rotation animation functions
  const startRotationAnimation = () => {
    refreshRotation.setValue(0);
    Animated.loop(
      Animated.timing(refreshRotation, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    ).start();
  };

  const stopRotationAnimation = () => {
    Animated.timing(refreshRotation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  // Swipe gesture handler for back navigation
  const swipeGestureHandler = PanResponder.create({
    onStartShouldSetPanResponder: () => false, // Don't capture immediately
    onStartShouldSetPanResponderCapture: () => false, // Don't capture on start
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to significant horizontal swipes
      const { dx, dy } = gestureState;

      // Only capture if it's a clear horizontal swipe with significant movement
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
      // Same logic for capture
      const { dx, dy } = gestureState;
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onPanResponderGrant: () => {
      // Gesture has been granted
    },
    onPanResponderMove: () => {
      // Handle move if needed
    },
    onPanResponderRelease: (evt, gestureState) => {
      const { dx, dy } = gestureState;
      const isHorizontalSwipe = Math.abs(dx) > Math.abs(dy);
      const swipeDistance = Math.abs(dx);

      // Only handle horizontal swipes with sufficient distance
      if (isHorizontalSwipe && swipeDistance > 100) {
        if (dx > 0) {
          // Swipe right - navigate back to Portal
          console.log('🔄 CMS: Swipe right detected, navigating to Portal');
          navigation.navigate('Portal');
        }
        // Ignore swipe left since we don't have sidebar anymore
      }
    },
    onPanResponderTerminationRequest: () => false, // Don't allow termination once we have it
    onShouldBlockNativeResponder: () => true, // Block native responders only when we have the gesture
  });





  // Load credentials and setup CMS URL
  const loadCredentialsAndSetupCMS = async () => {
    try {
      console.log('Loading credentials for CMS...');

      // Check if we're in demo mode first
      const isDemoMode = await checkDemoMode();
      if (isDemoMode) {
        console.log('🎭 Demo mode detected - loading demo CMS data');

        // Show loading briefly for realistic feel
        setIsLoading(true);

        // Load demo data with a slight delay
        setTimeout(() => {
          const demoCMSData = Object.keys(DEMO_CMS_DATA).map(semesterName => ({
            season: semesterName.includes('Fall') ? '65' : '64',
            seasonType: semesterName.split(' ')[0],
            title: semesterName,
            courses: DEMO_CMS_DATA[semesterName].map(course => ({
              code: course.courseCode,
              name: course.courseName,
              id: course.id,
              sid: course.sid,
              instructor: course.instructor,
              credits: course.credits
            }))
          }));

          setSemestersData(demoCMSData);
          setIsLoading(false);
          console.log('🎭 Demo CMS data loaded:', demoCMSData.length, 'semesters');
        }, 200);

        return;
      }

      // Clear WebView session before setting up CMS
      await clearWebViewSession();

      // Get stored credentials from localStorage
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('No stored credentials found');
        Alert.alert(
          'Authentication Required',
          'Please login first to access CMS.',
          [
            {
              text: 'Go to Login',
              onPress: () => navigation.navigate('Login')
            }
          ]
        );
        return;
      }

      console.log('Credentials loaded, checking cache...');

      // Try to load cached data first
      const hasCached = await loadCachedData();

      if (hasCached) {
        // We have cached data, show it immediately and set loading to false
        setIsLoading(false);
        console.log('Cached data loaded immediately, starting background refresh...');

        // Start background refresh
        setIsBackgroundRefreshing(true);
        const encodedUsername = encodeURIComponent(storedUsername);
        const encodedPassword = encodeURIComponent(storedPassword);
        const urlWithCredentials = `https://${encodedUsername}:${encodedPassword}@cms.guc.edu.eg/apps/student/ViewAllCourseStn`;
        setCmsUrl(urlWithCredentials);
        console.log('CMS URL prepared for background refresh');
      } else {
        // No cached data, show loading and fetch fresh data
        console.log('No cached data, fetching fresh data...');
        const encodedUsername = encodeURIComponent(storedUsername);
        const encodedPassword = encodeURIComponent(storedPassword);
        const urlWithCredentials = `https://${encodedUsername}:${encodedPassword}@cms.guc.edu.eg/apps/student/ViewAllCourseStn`;
        setCmsUrl(urlWithCredentials);
        console.log('CMS URL prepared for initial load');
      }
    } catch (error) {
      console.log('Error loading credentials:', error);
      setIsLoading(false);
    }
  };

  // Handle refresh button press
  const handleRefresh = async () => {
    console.log('Refresh button pressed - CMS screen');
    setIsRefreshing(true);

    // Clear existing data
    setSemestersData([]);

    // Reload WebView to fetch fresh data
    if (webViewRef.current && cmsUrl) {
      webViewRef.current.reload();
    }
  };

  // Handle WebView load completion
  const handleWebViewLoad = () => {
    console.log('CMS WebView loaded, injecting JavaScript...');

    if (webViewRef.current) {
      // Wait a moment for page to fully render
      setTimeout(() => {
        injectExtractionCode();
      }, 2000);
    }
  };

  // Inject JavaScript to extract semester data
  const injectExtractionCode = () => {
    if (webViewRef.current) {
      const jsCode = `
        (function() {
          try {
            console.log('Starting CMS data extraction...');
            console.log('Page title:', document.title);
            console.log('Page URL:', window.location.href);
            console.log('Document ready state:', document.readyState);

            // Look for the main content area that contains semester data
            const semesters = [];

            // Now extract the actual semester data based on the real HTML structure
            console.log('Extracting semester data from CMS page...');
            
            // Find all semester title elements
            const semesterTitleElements = document.querySelectorAll('.menu-header-title');
            console.log('Found', semesterTitleElements.length, 'semester title elements');

            // Log all title elements for debugging
            semesterTitleElements.forEach((element, index) => {
              console.log('Title element', index + 1, ':', element.textContent.trim());
            });
            
            // Find all course tables
            const courseTables = document.querySelectorAll('table[id*="GridView1"]');
            console.log('Found', courseTables.length, 'course tables');
            
            // Extract semester data - only process valid semester titles
            let validSemesterIndex = 0;
            semesterTitleElements.forEach((titleElement, index) => {
              const titleText = titleElement.textContent.trim();
              console.log('Processing semester title:', titleText);

              // Parse the title format: "Season : 65   , Title: Spring 2025   "
              const seasonMatch = titleText.match(/Season\\s*:\\s*(\\d+)/i);
              const titleMatch = titleText.match(/Title\\s*:\\s*([^,\\n\\r]+)/i);

              const seasonId = seasonMatch ? seasonMatch[1].trim() : '';
              const semesterTitle = titleMatch ? titleMatch[1].trim() : '';

              console.log('Parsed - Season ID:', seasonId, 'Title:', semesterTitle);

              // Skip if we couldn't parse season or title properly - these are likely invalid elements
              if (!seasonId || !semesterTitle || semesterTitle.length < 3) {
                console.log('Skipping invalid semester element:', titleText);
                return;
              }

              // Determine season type from title
              let seasonType = 'Unknown';
              const lowerTitle = semesterTitle.toLowerCase();
              if (lowerTitle.includes('fall')) {
                seasonType = 'Fall';
              } else if (lowerTitle.includes('spring')) {
                seasonType = 'Spring';
              } else if (lowerTitle.includes('summer')) {
                seasonType = 'Summer';
              }

              const semester = {
                season: seasonId, // Use the season number (65, 64, etc.)
                seasonType: seasonType, // Fall, Spring, Summer
                title: semesterTitle,
                courses: []
              };

              // Find the corresponding course table for this semester using validSemesterIndex
              const correspondingTable = courseTables[validSemesterIndex];
              if (correspondingTable) {
                console.log('Processing table for semester:', semesterTitle);
                console.log('Table ID:', correspondingTable.id);
                
                // Extract courses from the table rows (skip header row)
                const courseRows = correspondingTable.querySelectorAll('tbody tr');
                console.log('Found', courseRows.length, 'rows in table');
                
                courseRows.forEach((row, rowIndex) => {
                  // Skip header row (first row)
                  if (rowIndex === 0) return;
                  
                  const cells = row.querySelectorAll('td');
                  if (cells.length >= 2) {
                    // Course name is in the second cell (index 1)
                    const courseNameCell = cells[1];
                    const courseName = courseNameCell.textContent.trim();
                    
                    // Extract course code from the course name (format: "(|DMET602|) Network & Media lab (403)")
                    const codeMatch = courseName.match(/\\(\\|([^|]+)\\|\\)/);
                    const courseCode = codeMatch ? codeMatch[1] : '';

                    // Extract clean course name (remove code and ID parts)
                    let cleanCourseName = courseName;
                    if (codeMatch) {
                      cleanCourseName = courseName.replace(/\\(\\|[^|]+\\|\\)\\s*/, '');
                    }
                    // Remove trailing ID in parentheses like "(403)"
                    cleanCourseName = cleanCourseName.replace(/\\s*\\(\\d+\\)\\s*$/, '');

                    // Extract id and sid from hidden columns
                    let courseId = '';
                    let courseSid = '';

                    // The id and sid are in the last two hidden td elements with display:none
                    if (cells.length >= 4) {
                      // Get the last two cells (they should be hidden with display:none)
                      const lastCells = Array.from(cells).slice(-2);
                      courseId = lastCells[0] ? lastCells[0].textContent.trim() : '';
                      courseSid = lastCells[1] ? lastCells[1].textContent.trim() : '';
                    }

                    console.log('Course found - Code:', courseCode, 'Name:', cleanCourseName, 'ID:', courseId, 'SID:', courseSid);

                    if (courseCode || cleanCourseName) {
                      semester.courses.push({
                        code: courseCode,
                        name: cleanCourseName,
                        id: courseId,
                        sid: courseSid
                      });
                    }
                  }
                });
              } else {
                console.log('No corresponding table found for semester:', semesterTitle);
              }

              semesters.push(semester);
              console.log('Semester processed:', semester.title, 'with', semester.courses.length, 'courses');

              // Increment the valid semester index for table matching
              validSemesterIndex++;
            });
            
            console.log('Extracted', semesters.length, 'semesters');
            console.log('Semesters data:', JSON.stringify(semesters, null, 2));
            
            // Send data back to React Native
            if (window.ReactNativeWebView) {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'cms_data_extracted',
                semesters: semesters,
                success: true,
                debugInfo: {
                  pageTitle: document.title,
                  pageUrl: window.location.href,
                  tablesCount: courseTables.length,
                  semesterTitlesCount: semesterTitleElements.length
                }
              }));
            }
            
          } catch (error) {
            console.error('Error extracting CMS data:', error);
            console.error('Error stack:', error.stack);
            
            if (window.ReactNativeWebView) {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'cms_data_extracted',
                error: error.message,
                errorStack: error.stack,
                success: false
              }));
            }
          }
        })();
        true;
      `;

      console.log('Injecting CMS extraction JavaScript...');
      webViewRef.current.injectJavaScript(jsCode);
    }
  };

  // Handle messages from WebView
  const handleWebViewMessage = async (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('CMS WebView message:', data.type);

      // Ignore data extraction messages when navigating to a course
      if (isNavigatingToCourse && data.type === 'cms_data_extracted') {
        console.log('Ignoring data extraction - navigating to course');
        return;
      }

      switch (data.type) {
        case 'cms_data_extracted':
          if (data.success) {
            console.log('CMS data extracted successfully:', data.semesters);

            // Check if this is different from current data (for background refresh)
            const currentDataString = JSON.stringify(semestersData);
            const newDataString = JSON.stringify(data.semesters);
            const isDataDifferent = currentDataString !== newDataString;

            // Always update the data
            safeSetState(setSemestersData, data.semesters, 'semestersData');

            // Save to cache
            await saveCacheData(data.semesters);

            if (isBackgroundRefreshing) {
              console.log('Background refresh completed. Data changed:', isDataDifferent);
              safeSetState(setIsBackgroundRefreshing, false, 'isBackgroundRefreshing');
            }
          } else {
            console.log('CMS data extraction failed:', data.error);
          }

          // Stop loading states
          safeSetState(setIsLoading, false, 'isLoading');
          safeSetState(setIsRefreshing, false, 'isRefreshing');
          safeSetState(setIsBackgroundRefreshing, false, 'isBackgroundRefreshing');
          break;

        default:
          console.log('Unknown message type:', data.type);
          break;
      }
    } catch (error) {
      console.log('Error parsing WebView message:', error);
      safeSetState(setIsLoading, false, 'isLoading');
      safeSetState(setIsRefreshing, false, 'isRefreshing');
      safeSetState(setIsBackgroundRefreshing, false, 'isBackgroundRefreshing');
    }
  };

  // Handle WebView errors
  const handleWebViewError = (syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    console.log('CMS WebView error:', nativeEvent);
    safeSetState(setIsLoading, false, 'isLoading');
    safeSetState(setIsRefreshing, false, 'isRefreshing');
    safeSetState(setIsBackgroundRefreshing, false, 'isBackgroundRefreshing');
  };

  // Create dynamic header layout
  const headerLayout = getResponsiveHeaderLayout('CMS', theme, safeCurrentThemeName);

  // Create styles function that uses theme
  const styles = createStyles(theme, safeCurrentThemeName, headerLayout);

  return (
    <ThemeTransitionWrapper>
      <View style={{ flex: 1 }} {...swipeGestureHandler.panHandlers}>
        <SafeAreaView style={styles.container}>
          {/* Back Button */}
          <View style={[headerLayout.leftButtonContainer, { top: 50 }]}>
            <TouchableOpacity
              style={[
                headerLayout.styles.buttonContainer,
                (isRefreshing || isLoading || isBackgroundRefreshing) && headerLayout.styles.buttonContainerLoading
              ]}
              onPress={() => navigation.navigate('Portal')}
            >
              <BackIcon size={20} color={theme.colors.primary} strokeWidth={3} />
            </TouchableOpacity>
          </View>

          {/* CMS Title */}
          <View style={[headerLayout.styles.titleContainer, { top: 55 }]}>
            <Text style={headerLayout.styles.titleText}>CMS</Text>
          </View>

          {/* Refresh Button */}
          <View style={[headerLayout.rightButtonContainer, { top: 50 }]}>
            <TouchableOpacity
              style={[
                headerLayout.styles.buttonContainer,
                (isRefreshing || isLoading || isBackgroundRefreshing) && headerLayout.styles.buttonContainerLoading
              ]}
              onPress={handleRefresh}
              disabled={isRefreshing}
            >
              <Animated.View
                style={{
                  transform: [{
                    rotate: refreshRotation.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '360deg']
                    })
                  }]
                }}
              >
                <RefreshIcon
                  size={24}
                  color={(isRefreshing || isLoading || isBackgroundRefreshing) ? '#808080' : safeCurrentThemeName === 'navy' ? '#DC2626' : '#f1c40f'}
                  strokeWidth={3}
                />
              </Animated.View>
            </TouchableOpacity>
          </View>

          {/* Main Content */}
          <View style={[styles.mainContent, getContentPaddingForHeader(headerLayout).contentContainer]}>
            {isLoading && !hasCachedData ? (
              <ScrollView
                style={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
              >
                <View style={styles.semestersContainer}>
                  {/* Ghost loading for semesters */}
                  {[1, 2, 3].map((index) => (
                    <View key={index} style={[
                      styles.ghostSemesterContainer,
                      {
                        backgroundColor: theme.colors.surface,
                        borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
                        borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 1,
                      }
                    ]}>
                      {/* Ghost semester title */}
                      <ShimmerBox
                        style={[styles.ghostLoading, styles.ghostSemesterTitle]}
                        theme={theme}
                      />

                      {/* Ghost courses */}
                      {[1, 2, 3, 4, 5].map((courseIndex) => (
                        <View key={courseIndex} style={[
                          styles.ghostCourseItem,
                          {
                            backgroundColor: theme.colors.surface,
                            borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
                            borderWidth: safeCurrentThemeName === 'colorful' ? 2 : 1,
                          }
                        ]}>
                          <View style={styles.ghostCourseContent}>
                            <View style={styles.ghostCourseIconContainer}>
                              <ShimmerBox
                                style={[styles.ghostLoading, styles.ghostCourseIcon]}
                                theme={theme}
                              />
                            </View>
                            <View style={styles.ghostCourseTextContainer}>
                              <ShimmerBox
                                style={[styles.ghostLoading, styles.ghostCourseName]}
                                theme={theme}
                              />
                            </View>
                            <View style={styles.ghostCourseCodeContainer}>
                              <ShimmerBox
                                style={[styles.ghostLoading, styles.ghostCourseCode]}
                                theme={theme}
                              />
                            </View>
                          </View>
                        </View>
                      ))}
                    </View>
                  ))}
                </View>
              </ScrollView>
            ) : (
              <ScrollView
                style={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
              >
                {semestersData.length > 0 ? (
                  <View style={styles.semestersContainer}>
                    {semestersData.map((semester, index) => (
                      <SemesterComponent
                        key={index}
                        semester={semester}
                        theme={theme}
                        safeCurrentThemeName={safeCurrentThemeName}
                        navigation={navigation}
                        webViewRef={webViewRef}
                        setIsNavigatingToCourse={setIsNavigatingToCourse}
                      />
                    ))}
                  </View>
                ) : (
                  <View style={styles.noDataContainer}>
                    <Text style={styles.noDataText}>
                      No semester data available. Please refresh to load data.
                    </Text>
                  </View>
                )}
              </ScrollView>
            )}
          </View>

          {/* WebView for CMS data extraction - can be visible or hidden */}
          {cmsUrl && (
            <View style={showWebView ? styles.visibleWebView : styles.hiddenWebView}>
              <WebView
                ref={webViewRef}
                source={{ uri: cmsUrl }}
                onLoadStart={() => {
                  console.log('WebView started loading');
                  // Don't set isLoading here as it interferes with refresh states
                  // isLoading should only be true during initial component load
                }}
                onLoad={handleWebViewLoad}
                onMessage={handleWebViewMessage}
                onError={handleWebViewError}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                mixedContentMode="compatibility"
                cacheEnabled={false}
                incognito={true}
                // iOS-specific props
                allowsInlineMediaPlayback={true}
                mediaPlaybackRequiresUserAction={false}
                allowsBackForwardNavigationGestures={false}
                // Security and compatibility
                originWhitelist={['*']}
                allowUniversalAccessFromFileURLs={true}
                allowFileAccessFromFileURLs={true}
                // Ensure JavaScript injection works on iOS
                injectedJavaScript=""
                onLoadEnd={handleWebViewLoad}
              />
            </View>
          )}


        </SafeAreaView>
      </View>
    </ThemeTransitionWrapper>
  );
};

// Create styles function that uses theme and header layout
const createStyles = (theme, safeCurrentThemeName, headerLayout) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  mainContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  semestersContainer: {
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 15,
    padding: 20,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : theme.colors.border,
    marginTop: 50,
  },
  noDataText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  visibleWebView: {
    position: 'absolute',
    top: 160,
    left: 20,
    right: 20,
    bottom: 20,
    borderRadius: 10,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: theme.colors.primary,
  },
  hiddenWebView: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? -100000 : -1000,
    left: Platform.OS === 'ios' ? -100000 : -1000,
    width: Platform.OS === 'ios' ? 0 : 1,
    height: Platform.OS === 'ios' ? 0 : 1,
    overflow: 'hidden',
  },
  // Ghost loading styles
  ghostLoading: {
    backgroundColor: theme.colors.textSecondary,
    opacity: 0.3,
    borderRadius: 4,
  },
  ghostSemesterContainer: {
    marginBottom: 20,
    borderRadius: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  ghostSemesterTitle: {
    height: 24,
    width: '60%',
    marginBottom: 15,
    alignSelf: 'center',
  },
  ghostCourseItem: {
    marginBottom: 10,
    borderRadius: 10,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  ghostCourseContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  ghostCourseIconContainer: {
    marginRight: 12,
    width: 24,
  },
  ghostCourseIcon: {
    width: 20,
    height: 20,
    borderRadius: 4,
  },
  ghostCourseTextContainer: {
    flex: 1,
    marginRight: 15,
  },
  ghostCourseName: {
    height: 16,
    width: '80%',
  },
  ghostCourseCodeContainer: {
    minWidth: 80,
  },
  ghostCourseCode: {
    height: 14,
    width: 60,
  },
});

export default CMSScreen;
